Formulaes

=IF(LEN(J22), IF(OR(AND(OR(J21="High",J22="Highest"), G22-F22<TIME(23,0,1)), AND(J22="Medium", G22-F22<TIME(2,0,1)), AND(OR(J22="Low",J22="Lowest"), G22-F22<TIME(1,0,1))), "Met", "Not Met"), "")

=IF(LEN(J21), IF(OR(AND(OR(J21="High",J21="Highest"),G21-21-F21<TIME(23,0,1)),AND(OR(J21="Medium",J21="Moderate"),G21-21-F21<TIME(2,0,1)),AND(OR(J21="Low",J21="Lowest"),G21-21-F21<TIME(1,0,1))),"Met","Not Met"), "")



f'=IF(LEN({chr(65 + priority_index)}{row}), IF(OR(AND(OR({chr(65 + priority_index)}{row}="High",{chr(65 + priority_index)}{row}="Highest"), {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(23,0,1)), AND({chr(65 + priority_index)}{row}="Medium", {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(2,0,1)), AND(OR({chr(65 + priority_index)}{row}="Low",{chr(65 + priority_index)}{row}="Lowest"), {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(1,0,1))), "Met", "Not Met"), "")'

{chr(65 + sla_status_index)}{row}
{chr(65 + priority_index)}{row}
{chr(65 + notification_index)}{row}
{chr(65 + alert_index)}{row}



worksheet.write_formula(f'{chr(65 + sla_status_index)}{row}', f'=IF(LEN({chr(65 + priority_index)}{row}), IF(OR(AND(OR({chr(65 + priority_index)}{row}="High",{chr(65 + priority_index)}{row}="Highest"),{chr(65 + notification_index)}{row}-{row}-{chr(65 + alert_index)}{row}<TIME(23,0,1)),AND(OR({chr(65 + priority_index)}{row}="Medium",{chr(65 + priority_index)}{row}="Moderate"),{chr(65 + notification_index)}{row}-{row}-{chr(65 + alert_index)}{row}<TIME(2,0,1)),AND(OR({chr(65 + priority_index)}{row}="Low",{chr(65 + priority_index)}{row}="Lowest"),{chr(65 + notification_index)}{row}-{row}-{chr(65 + alert_index)}{row}<TIME(1,0,1))),"Met","Not Met"), "")')

---------------------------------------------------------------------------------------------------------------------------------------


project = "MSOC" and "customer organization[dropdown]" = "PDRM Firewall" and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY created DESC

        
PDRM FIREWALL

TM/PDRM Holding Time; =TEXT((N1222-P1222),"[hh]:mm:ss")

Total Time taken; =SUM(I1221-C1221)

TL Holding Time; =IF(OR(O1221="",C1221=""),"",TEXT(O1221-C1221,"hh:mm"))

pdrm_ack_time_index = df.columns.get_loc('Custom field (PDRM Acknowledge Time)') # column P 		{chr(65 + pdrm_ack_time_index)}{row}
holding_time_index = df.columns.get_loc('Custom field (TM/PDRM Holding Time)') # column N 		{chr(65 + holding_time_index)}{row}
ttl_time_index = df.columns.get_loc('Custom field (Total Time Taken)') # Column O   			{chr(65 + ttl_time_index)}{row}
closing_time_index = df.columns.get_loc('Custom field (Closing Time)') # column J    			{chr(65 + closing_time_index)}{row}
start_time_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)') # Column D	{chr(65 + start_time_index)}{row}
pdrm_holding_index = df.columns.get_loc('Custom field (PDRM TL Holding Time)') # column Q		{chr(65 + pdrm_holding_index)}{row}


f'{chr(65 + holding_time_index)}{row}',f'=TEXT(({chr(65 + holding_time_index)}{row}-{chr(65 + pdrm_ack_time_index)}{row}),"[hh]:mm:ss")'

f'{chr(65 + ttl_time_index)}{row}',f'=SUM({chr(65 + closing_time_index)}{row}-{chr(65 + start_time_index)}{row})'

f'{chr(65 + pdrm_holding_index)}{row}',f'=IF(OR({chr(65 + ttl_time_index)}{row}="",C1221=""),"",TEXT({chr(65 + ttl_time_index)}{row}-{chr(65 + start_time_index)}{row},"hh:mm"))'








