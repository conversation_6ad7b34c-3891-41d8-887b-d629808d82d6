Target sheet:
Issue key 
Issue id
Client ticket number 
Tool incident id
Customer Organization
summary
Alert Generation Date/Time
Notification Sent out Date/Time
Duration
SLA status
Priority
Analyst Name
Incident Source/Tool
Source IP/Hostname
Username/hostname/log source
Destination IP
EPF collector IP
PDRM Fault Category 
PDRM Acknowledge Venue
Detection/Impact
Status
Month
Resolution Notes
Last Follow up
Closing Time
Resolution

Unmodified sheet:
Summary
Issue Key 
Issue id
Status
Priority
Resolution
Alert Generation Date/Time
Analyst Name
Client Ticket Number
Closing Time
Customer Organization
Destination IP
Detection/Impact
Duration
EPF collector IP
Incident Source/Tool
Last Follow up
Month
Notification Sent out Date/Time
PDRM Acknowledge Venue
PDRM Fault category
Resolutions Notes
SLA Status
Source IP/Hostname
Tool Incident ID
Username/Hostname/Log Source


///////////////////////////////// 
Formula: 
Custom field (SLA Status) =IF(LEN(J889), IF(OR(AND(J889="High", G889-F889<TIME(1,0,1)), AND(J889="Medium", G889-F889<TIME(2,0,1)), AND(J889="Low", G889-F889<TIME(23,0,1))), "Met", "Not Met"), "")
Custom field (Duration) =IF(F889="", "", TEXT(G889-F889,"hh:mm:ss"))

=IF(LEN(J2), IF(OR(AND(J2="High", G2-F2<TIME(1,0,1)), AND(J2="Medium", G2-F2<TIME(2,0,1)), AND(J2="Low", G2-F2<TIME(23,0,1))), "Met", "Not Met"), "")