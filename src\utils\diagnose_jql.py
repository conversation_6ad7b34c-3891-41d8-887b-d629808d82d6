#!/usr/bin/env python3
"""
JQL Diagnostic Script

This script helps diagnose JQL query issues by testing different client configurations
and providing detailed error information.
"""

import json
from client_config import ClientConfig


def test_jql_generation():
    """Test JQL generation with different client configurations"""
    print("=== JQL Generation Diagnostic ===\n")
    
    # Test 1: Original client list
    original_clients = ["MAG", "PDRM", "Astro", "Firefly", "EPF", "UTP", "Jupem", "UMWT", "MIDF"]
    original_jql = ', '.join(original_clients)
    print("1. Original Client List:")
    print(f"   Clients: {original_jql}")
    print(f"   Length: {len(original_jql)} characters")
    
    # Test 2: New client list
    new_clients = ClientConfig.get_msoc_clients()
    new_jql = ClientConfig.get_jql_client_list()
    print(f"\n2. New Client List:")
    print(f"   Clients: {new_jql}")
    print(f"   Length: {len(new_jql)} characters")
    print(f"   Total clients: {len(new_clients)}")
    
    # Test 3: Check for problematic characters
    print(f"\n3. Character Analysis:")
    has_quotes = '"' in new_jql
    has_special = any(c in new_jql for c in ['&', '<', '>', '|', ';'])
    print(f"   Contains quotes: {has_quotes}")
    print(f"   Contains special chars: {has_special}")
    
    # Test 4: Build complete JQL queries
    base_jql = 'project = "MSOC" and "customer organization[dropdown]" in ({}) and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY cf[10086] DESC, created DESC'
    
    original_full_jql = base_jql.format(original_jql)
    new_full_jql = base_jql.format(new_jql)
    
    print(f"\n4. Complete JQL Queries:")
    print(f"   Original JQL length: {len(original_full_jql)} characters")
    print(f"   New JQL length: {len(new_full_jql)} characters")
    
    print(f"\n   Original JQL:")
    print(f"   {original_full_jql}")
    
    print(f"\n   New JQL:")
    print(f"   {new_full_jql}")
    
    # Test 5: Check for differences
    print(f"\n5. Differences:")
    new_client_set = set(new_clients)
    original_client_set = set(original_clients)
    
    added_clients = new_client_set - original_client_set
    removed_clients = original_client_set - new_client_set
    
    print(f"   Added clients: {list(added_clients)}")
    print(f"   Removed clients: {list(removed_clients)}")
    
    return original_full_jql, new_full_jql


def test_client_validation():
    """Test client validation"""
    print("\n=== Client Validation ===\n")
    
    # Test all clients
    all_clients = ClientConfig.get_all_clients()
    validation = ClientConfig.validate_client_list(all_clients)
    
    print(f"Total clients: {len(all_clients)}")
    print(f"Valid clients: {len(validation['valid'])}")
    print(f"Invalid clients: {len(validation['invalid'])}")
    
    if validation['invalid']:
        print(f"Invalid clients found: {validation['invalid']}")
    
    # Test MSOC clients specifically
    msoc_clients = ClientConfig.get_msoc_clients()
    msoc_validation = ClientConfig.validate_client_list(msoc_clients)
    
    print(f"\nMSOC clients: {len(msoc_clients)}")
    print(f"Valid MSOC clients: {len(msoc_validation['valid'])}")
    print(f"Invalid MSOC clients: {len(msoc_validation['invalid'])}")
    
    if msoc_validation['invalid']:
        print(f"Invalid MSOC clients: {msoc_validation['invalid']}")


def suggest_fixes():
    """Suggest potential fixes for JQL issues"""
    print("\n=== Suggested Fixes ===\n")
    
    print("1. JQL Query Length:")
    print("   - Jira has a maximum JQL query length limit")
    print("   - Consider splitting into multiple queries if too long")
    
    print("\n2. Client Name Issues:")
    print("   - Ensure all client names exist in Jira")
    print("   - Check for exact spelling and case sensitivity")
    print("   - Verify special characters are properly escaped")
    
    print("\n3. Custom Field Issues:")
    print("   - Verify 'customer organization[dropdown]' field exists")
    print("   - Check field permissions and visibility")
    print("   - Confirm field ID (cf[10086]) is correct")
    
    print("\n4. Fallback Strategy:")
    print("   - Use original client list as fallback")
    print("   - Add new clients gradually")
    print("   - Test each client individually")
    
    print("\n5. Debugging Steps:")
    print("   - Enable JQL query logging")
    print("   - Test queries in Jira web interface")
    print("   - Check Jira API response details")


def generate_test_queries():
    """Generate test queries for manual testing"""
    print("\n=== Test Queries for Manual Testing ===\n")
    
    # Simple test query
    simple_query = 'project = "MSOC"'
    print(f"1. Simple Query:")
    print(f"   {simple_query}")
    
    # Test with one new client
    single_new_client = 'project = "MSOC" and "customer organization[dropdown]" = "EPF Guardium"'
    print(f"\n2. Single New Client Test:")
    print(f"   {single_new_client}")
    
    # Test with original clients only
    original_clients = ["MAG", "PDRM", "Astro", "Firefly", "EPF", "UTP", "Jupem", "UMWT", "MIDF"]
    original_jql = ', '.join(original_clients)
    original_query = f'project = "MSOC" and "customer organization[dropdown]" in ({original_jql})'
    print(f"\n3. Original Clients Only:")
    print(f"   {original_query}")
    
    # Test with quoted clients
    quoted_clients = ['"EPF Guardium"', '"NADI Tech"', '"EPF SIEM"']
    quoted_jql = ', '.join(quoted_clients)
    quoted_query = f'project = "MSOC" and "customer organization[dropdown]" in ({quoted_jql})'
    print(f"\n4. Quoted Clients Only:")
    print(f"   {quoted_query}")


def main():
    """Run all diagnostic tests"""
    print("SOC Jira Tracker - JQL Diagnostic Tool")
    print("=" * 50)
    
    try:
        # Run tests
        test_jql_generation()
        test_client_validation()
        suggest_fixes()
        generate_test_queries()
        
        print("\n" + "=" * 50)
        print("Diagnostic completed successfully!")
        print("\nNext steps:")
        print("1. Test the generated queries manually in Jira")
        print("2. Check Jira logs for detailed error messages")
        print("3. Verify all client names exist in your Jira instance")
        print("4. Consider using the fallback query if issues persist")
        
    except Exception as e:
        print(f"\nError during diagnostic: {str(e)}")
        print("Please check your client configuration.")


if __name__ == "__main__":
    main()
