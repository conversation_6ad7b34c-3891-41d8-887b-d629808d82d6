# SOC Jira Tracker - Clean Architecture Version

A comprehensive SOC (Security Operations Center) Jira tracking and automation tool with clean, organized architecture.

## 🏗️ Project Structure

```
soc_jira_tracker/
├── src/                          # Source code
│   ├── core/                     # Core business logic
│   │   └── jira_main.py         # Main application logic
│   ├── config/                   # Configuration management
│   │   └── client_config.py     # Client configuration
│   └── utils/                    # Utility functions
│       ├── data_validation.py   # Data validation utilities
│       ├── gdrive_method.py     # Google Drive operations
│       ├── pdrm_firewall_formatter.py  # Data formatting
│       └── diagnose_jql.py      # JQL diagnostic tools
├── tests/                        # Test suite
│   ├── test_client_config.py    # Client config tests
│   ├── test_data_validation.py  # Data validation tests
│   └── run_tests.py             # Test runner
├── config/                       # Configuration files
│   ├── app.env                  # Environment variables
│   ├── client_secrets.json     # Google API credentials
│   ├── jira_one_config.json    # Jira configuration
│   └── requirements.txt         # Python dependencies
├── docs/                         # Documentation
│   ├── IMPLEMENTATION_SUMMARY.md
│   ├── ISSUES_FOUND_AND_FIXES.md
│   └── Guides/                  # User guides
├── scripts/                      # Utility scripts
│   └── start.bat               # Main startup script
├── data/                         # Data storage
├── logs/                         # Application logs
│   └── archive/                # Archived logs
├── EXPORT/                       # Export directory
└── start.bat                    # Entry point
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Required Python packages (see `config/requirements.txt`)
- Jira access credentials
- Google Drive API credentials (optional)

### Installation

1. **Clone/Download the project**
2. **Install dependencies:**
   ```bash
   pip install -r config/requirements.txt
   ```
3. **Configure credentials:**
   - Update `config/app.env` with your environment variables
   - Place your Google API credentials in `config/client_secrets.json`
   - Configure Jira settings in `config/jira_one_config.json`

### Running the Application

**Option 1: Use the startup script (Recommended)**
```bash
# Windows
start.bat

# Or directly
scripts/start.bat
```

**Option 2: Run directly**
```bash
# Run main application
python -m src.core.jira_main

# Run system validation
python -m src.utils.data_validation

# Run tests
python tests/run_tests.py
```

## 🧪 Testing

The project includes a comprehensive test suite:

```bash
# Run all tests
python tests/run_tests.py

# Run specific test module
python tests/run_tests.py test_client_config

# Run individual test files
python -m unittest tests.test_client_config
python -m unittest tests.test_data_validation
```

## 📋 Features

- **MSOC Export**: Export MSOC tickets to CSV format
- **PDRM Firewall Export**: Specialized export for PDRM Firewall tickets
- **Data Validation**: Comprehensive data validation and integrity checks
- **Google Drive Integration**: Automatic upload to Google Drive
- **Client Management**: Centralized client configuration
- **Logging**: Comprehensive logging with file and console output
- **Error Handling**: Robust error handling and recovery

## 🔧 Configuration

### Client Configuration
Edit `src/config/client_config.py` to manage client organizations:
- Add new clients to `ALL_CLIENTS`
- Update `MSOC_CLIENTS` for standard exports
- Configure `FIREWALL_CLIENTS` for firewall-specific exports

### Environment Variables
Configure `config/app.env` with your settings:
```env
JIRA_SERVER=your-jira-server
JIRA_USERNAME=your-username
JIRA_TOKEN=your-api-token
```

## 📊 Data Export Formats

### MSOC Export
- Standard SOC ticket export
- Configurable client filtering
- Monthly date range filtering
- CSV format output

### PDRM Firewall Export
- Specialized format for PDRM Firewall tickets
- Structured data validation
- JSON and CSV output formats
- Custom field mapping

## 🛠️ Development

### Adding New Features
1. Create feature branch
2. Add code to appropriate `src/` subdirectory
3. Write tests in `tests/`
4. Update documentation
5. Run test suite to ensure compatibility

### Code Organization
- **Core Logic**: Place in `src/core/`
- **Utilities**: Place in `src/utils/`
- **Configuration**: Place in `src/config/`
- **Tests**: Place in `tests/`

## 📝 Logging

Logs are stored in the `logs/` directory:
- `logs/app.log`: Main application log
- `logs/archive/`: Archived log files

Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

## 🔍 Troubleshooting

### Common Issues
1. **Import Errors**: Ensure you're running from the project root
2. **Authentication Failures**: Check credentials in config files
3. **Missing Dependencies**: Run `pip install -r config/requirements.txt`

### Diagnostic Tools
- Run system validation: `python -m src.utils.data_validation`
- Check JQL queries: `python -m src.utils.diagnose_jql`
- View client configuration: `python -m src.config.client_config`

## 📄 License

This project is proprietary software for SOC operations.

## 🤝 Contributing

1. Follow the established code structure
2. Write tests for new features
3. Update documentation
4. Ensure all tests pass before submitting changes

---

For detailed implementation information, see `docs/IMPLEMENTATION_SUMMARY.md`
