@echo off
echo ===============================================
echo SOC Jira Tracker - Refactored Version
echo ===============================================
echo Current directory: %CD%
echo.

REM Try to activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found, using system Python
)

echo.
echo Choose an option:
echo 1. Run system validation tests (recommended first time)
echo 2. Run full SOC Jira Tracker
echo 3. Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Running system validation tests...
    python test_refactored_system.py
    echo.
    echo Press any key to continue...
    pause
    goto :start
) else if "%choice%"=="2" (
    echo.
    echo Running SOC Jira Tracker...
    python jira_main.py
) else if "%choice%"=="3" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice. Please try again.
    goto :start
)

echo.
echo Process completed. Press any key to exit...
pause