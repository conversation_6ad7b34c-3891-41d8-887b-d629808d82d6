@echo off
cd /d "%~dp0\.."
echo ===============================================
echo SOC Jira Tracker - Clean Architecture Version
echo ===============================================
echo Current directory: %CD%
echo.

REM Try to activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found, using system Python
)

echo.
echo Choose an option:
echo 1. Run system validation tests (recommended first time)
echo 2. Run full SOC Jira Tracker
echo 3. Run test suite
echo 4. Exit
echo.
set /p choice="Enter your choice (1-4): "

:start
if "%choice%"=="1" (
    echo.
    echo Running system validation tests...
    python -m src.utils.data_validation
    echo.
    echo Press any key to continue...
    pause
    goto :menu
) else if "%choice%"=="2" (
    echo.
    echo Running SOC Jira Tracker...
    python -m src.core.jira_main
) else if "%choice%"=="3" (
    echo.
    echo Running test suite...
    python tests\run_tests.py
    echo.
    echo Press any key to continue...
    pause
    goto :menu
) else if "%choice%"=="4" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice. Please try again.
    goto :menu
)

:menu
echo.
echo Choose an option:
echo 1. Run system validation tests (recommended first time)
echo 2. Run full SOC Jira Tracker
echo 3. Run test suite
echo 4. Exit
echo.
set /p choice="Enter your choice (1-4): "
goto :start

echo.
echo Process completed. Press any key to exit...
pause