[('SOC-Export', 
'project = "MSOC"\nand "customer organization[dropdown]" in (MAG, PDRM, Astro, Firefly, EPF, UTP, Jupem, UMWT, MIDF)\nORDER BY created DESC', 

'https://tls-mss.atlassian.net/rest/api/3/search?jql=project%20%3D%20%22MSOC%22%0Aand%20%22customer%20organization%5Bdropdown%5D%22%20in%20%28MAG%2C%20PDRM%2C%20Astro%2C%20Firefly%2C%20EPF%2C%20UTP%2C%20Jupem%2C%20UMWT%2C%20MIDF%29%0AORDER%20BY%20created%20DESC', 'https://tls-mss.atlassian.net/issues/?filter=10005'), 
('SOC-Export-PDRM Firewall', 
'project = "MSOC" and "customer organization[dropdown]" = "PDRM Firewall" ORDER BY created DESC', 'https://tls-mss.atlassian.net/rest/api/3/search?jql=project%20%3D%20%22MSOC%22%20and%20%22customer%20organization%5Bdropdown%5D%22%20%3D%20%22PDRM%20Firewall%22%20ORDER%20BY%20created%20DESC', 'https://tls-mss.atlassian.net/issues/?filter=10006')]


project = "MSOC"
and "customer organization[dropdown]" in (MAG, PDRM, Astro, Firefly, EPF, UTP, Jupem, UMWT, MIDF) and ('Notification Sent out Date/Time' >= startOfMonth() and 'Notification Sent out Date/Time' <= endOfMonth())'


'project = "MSOC"
and "customer organization[dropdown]" in (MAG, PDRM, Astro, Firefly, EPF, UTP, Jupem, UMWT, MIDF) and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth())
ORDER BY cf[10086] DESC, created DESC'