"""
Setup script for SOC Jira Tracker
"""

import os
import sys
import subprocess
from pathlib import Path

def create_virtual_environment():
    """Create a virtual environment for the project"""
    print("Creating virtual environment...")
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False

def install_dependencies():
    """Install project dependencies"""
    print("Installing dependencies...")
    try:
        if os.name == 'nt':  # Windows
            pip_path = "venv\\Scripts\\pip.exe"
        else:  # Unix/Linux/Mac
            pip_path = "venv/bin/pip"
        
        subprocess.run([pip_path, "install", "-r", "config/requirements.txt"], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_config_templates():
    """Create configuration file templates"""
    print("Creating configuration templates...")
    
    # Create app.env template
    env_template = """# SOC Jira Tracker Configuration
# Copy this file and update with your actual values

# Jira Configuration
JIRA_SERVER=https://your-jira-server.com
JIRA_USERNAME=your-username
JIRA_TOKEN=your-api-token

# Google Drive Configuration (Optional)
GDRIVE_FOLDER_ID=your-folder-id

# Logging Configuration
LOG_LEVEL=INFO
"""
    
    config_dir = Path("config")
    env_file = config_dir / "app.env.template"
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_template)
        print(f"✅ Created {env_file}")
        
        # Create jira_one_config.json template
        jira_template = """{
    "url": "https://your-jira-server.com",
    "username": "your-username",
    "password": "your-api-token"
}"""
        
        jira_file = config_dir / "jira_one_config.json.template"
        with open(jira_file, 'w') as f:
            f.write(jira_template)
        print(f"✅ Created {jira_file}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create config templates: {e}")
        return False

def setup_directories():
    """Ensure all necessary directories exist"""
    print("Setting up directories...")
    
    directories = [
        "logs/archive",
        "data",
        "EXPORT"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        print("✅ Directories created successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to create directories: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 50)
    print("SOC Jira Tracker - Setup Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("src").exists() or not Path("config").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    success = True
    
    # Setup steps
    success &= setup_directories()
    success &= create_config_templates()
    success &= create_virtual_environment()
    success &= install_dependencies()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Copy config templates and update with your credentials:")
        print("   - config/app.env.template → config/app.env")
        print("   - config/jira_one_config.json.template → config/jira_one_config.json")
        print("2. Add your Google API credentials to config/client_secrets.json")
        print("3. Run the application: start.bat")
        print("4. Run tests to verify setup: python tests/run_tests.py")
    else:
        print("❌ Setup completed with errors. Please check the messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
