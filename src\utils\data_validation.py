"""
Data Validation Module for SOC Jira Tracker

This module provides comprehensive validation functions for client data,
PDRM Firewall data structures, and general data integrity checks.
"""

import csv
import json
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from ..config.client_config import C<PERSON>Config, PDRMFirewallDataStructure


class DataValidator:
    """
    Comprehensive data validation class for SOC Jira Tracker
    """
    
    def __init__(self):
        self.client_config = ClientConfig()
        self.pdrm_structure = PDRMFirewallDataStructure()
    
    def validate_client_configuration(self) -> Dict[str, Any]:
        """
        Validate the current client configuration
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'valid': True,
            'total_clients': len(self.client_config.get_all_clients()),
            'msoc_clients': len(self.client_config.get_msoc_clients()),
            'firewall_clients': len(self.client_config.get_firewall_clients()),
            'errors': [],
            'warnings': []
        }
        
        # Check for duplicate clients
        all_clients = self.client_config.get_all_clients()
        if len(all_clients) != len(set(all_clients)):
            validation_results['valid'] = False
            validation_results['errors'].append("Duplicate clients found in configuration")
        
        # Check if MSOC clients are all valid
        msoc_validation = self.client_config.validate_client_list(
            self.client_config.get_msoc_clients()
        )
        if msoc_validation['invalid']:
            validation_results['valid'] = False
            validation_results['errors'].append(
                f"Invalid MSOC clients: {msoc_validation['invalid']}"
            )
        
        return validation_results
    
    def validate_csv_structure(self, csv_file_path: str, expected_fields: List[str]) -> Dict[str, Any]:
        """
        Validate CSV file structure against expected fields
        
        Args:
            csv_file_path: Path to CSV file
            expected_fields: List of expected field names
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'valid': True,
            'file_exists': False,
            'total_records': 0,
            'missing_fields': [],
            'extra_fields': [],
            'empty_records': 0,
            'errors': []
        }
        
        try:
            # Check if file exists
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                validation_results['file_exists'] = True
                reader = csv.DictReader(f)
                
                # Get actual fields
                actual_fields = reader.fieldnames or []
                
                # Check for missing and extra fields
                expected_set = set(expected_fields)
                actual_set = set(actual_fields)
                
                validation_results['missing_fields'] = list(expected_set - actual_set)
                validation_results['extra_fields'] = list(actual_set - expected_set)
                
                if validation_results['missing_fields']:
                    validation_results['valid'] = False
                
                # Count records and check for empty records
                records = list(reader)
                validation_results['total_records'] = len(records)
                
                for i, record in enumerate(records):
                    if all(not value.strip() for value in record.values()):
                        validation_results['empty_records'] += 1
        
        except FileNotFoundError:
            validation_results['valid'] = False
            validation_results['errors'].append(f"File not found: {csv_file_path}")
        except Exception as e:
            validation_results['valid'] = False
            validation_results['errors'].append(f"Error reading file: {str(e)}")
        
        return validation_results
    
    def validate_pdrm_firewall_data(self, data: List[Dict]) -> Dict[str, Any]:
        """
        Validate PDRM Firewall data structure and content
        
        Args:
            data: List of PDRM Firewall ticket dictionaries
            
        Returns:
            Dictionary with detailed validation results
        """
        validation_results = {
            'valid': True,
            'total_records': len(data),
            'field_validation': {
                'missing_fields': [],
                'extra_fields': [],
                'empty_required_fields': []
            },
            'data_quality': {
                'invalid_dates': [],
                'missing_issue_keys': [],
                'invalid_states': []
            },
            'derived_fields': {
                'formula_placeholders': [],
                'missing_formulas': []
            },
            'errors': [],
            'warnings': []
        }
        
        required_fields = self.pdrm_structure.get_field_order()
        derived_fields = self.pdrm_structure.get_derived_fields()
        valid_states = ['Open', 'In Progress', 'Resolved', 'Closed', 'Cancelled']
        
        for i, record in enumerate(data):
            record_num = i + 1
            
            # Validate field structure
            record_validation = self.pdrm_structure.validate_data_structure(record)
            
            if record_validation['missing']:
                validation_results['valid'] = False
                validation_results['field_validation']['missing_fields'].append({
                    'record': record_num,
                    'fields': record_validation['missing']
                })
            
            if record_validation['extra']:
                validation_results['field_validation']['extra_fields'].append({
                    'record': record_num,
                    'fields': record_validation['extra']
                })
            
            # Validate required field content
            if not record.get('issue_key', '').strip():
                validation_results['valid'] = False
                validation_results['data_quality']['missing_issue_keys'].append(record_num)
            
            # Validate states
            state = record.get('states', '').strip()
            if state and state not in valid_states:
                validation_results['data_quality']['invalid_states'].append({
                    'record': record_num,
                    'state': state
                })
            
            # Validate date fields
            date_fields = ['closing_time', 'pdrm_acknowledge_time']
            for field in date_fields:
                date_value = record.get(field, '').strip()
                if date_value and not self._is_valid_date_or_formula(date_value):
                    validation_results['data_quality']['invalid_dates'].append({
                        'record': record_num,
                        'field': field,
                        'value': date_value
                    })
            
            # Validate derived fields have formulas
            for field in derived_fields:
                field_value = record.get(field, '').strip()
                if field_value:
                    if field_value.startswith('='):
                        validation_results['derived_fields']['formula_placeholders'].append({
                            'record': record_num,
                            'field': field,
                            'formula': field_value
                        })
                    else:
                        validation_results['derived_fields']['missing_formulas'].append({
                            'record': record_num,
                            'field': field,
                            'value': field_value
                        })
        
        return validation_results
    
    def _is_valid_date_or_formula(self, value: str) -> bool:
        """Check if a value is a valid date or formula placeholder"""
        if value.startswith('='):
            return True  # Formula placeholder
        
        # Try to parse as date
        date_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%d/%m/%Y %H:%M',
            '%d/%b/%y %I:%M %p',
            '%Y-%m-%d',
            '%d/%m/%Y'
        ]
        
        for fmt in date_formats:
            try:
                datetime.strptime(value, fmt)
                return True
            except ValueError:
                continue
        
        return False
    
    def generate_validation_report(self, validation_results: Dict[str, Any], 
                                 report_type: str = "general") -> str:
        """
        Generate a human-readable validation report
        
        Args:
            validation_results: Results from validation functions
            report_type: Type of report ("general", "pdrm_firewall", "client_config")
            
        Returns:
            Formatted validation report string
        """
        report_lines = []
        report_lines.append(f"=== {report_type.upper()} VALIDATION REPORT ===")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Overall status
        status = "PASSED" if validation_results.get('valid', False) else "FAILED"
        report_lines.append(f"Overall Status: {status}")
        report_lines.append("")
        
        # Summary statistics
        if 'total_records' in validation_results:
            report_lines.append(f"Total Records: {validation_results['total_records']}")
        
        if 'total_clients' in validation_results:
            report_lines.append(f"Total Clients: {validation_results['total_clients']}")
            report_lines.append(f"MSOC Clients: {validation_results['msoc_clients']}")
            report_lines.append(f"Firewall Clients: {validation_results['firewall_clients']}")
        
        report_lines.append("")
        
        # Errors
        if validation_results.get('errors'):
            report_lines.append("ERRORS:")
            for error in validation_results['errors']:
                report_lines.append(f"  - {error}")
            report_lines.append("")
        
        # Warnings
        if validation_results.get('warnings'):
            report_lines.append("WARNINGS:")
            for warning in validation_results['warnings']:
                report_lines.append(f"  - {warning}")
            report_lines.append("")
        
        # Specific validation details
        if report_type == "pdrm_firewall":
            self._add_pdrm_validation_details(report_lines, validation_results)
        
        return "\n".join(report_lines)
    
    def _add_pdrm_validation_details(self, report_lines: List[str], 
                                   validation_results: Dict[str, Any]) -> None:
        """Add PDRM Firewall specific validation details to report"""
        
        # Field validation
        field_val = validation_results.get('field_validation', {})
        if field_val.get('missing_fields'):
            report_lines.append("MISSING REQUIRED FIELDS:")
            for item in field_val['missing_fields']:
                report_lines.append(f"  Record {item['record']}: {', '.join(item['fields'])}")
            report_lines.append("")
        
        # Data quality issues
        data_qual = validation_results.get('data_quality', {})
        if data_qual.get('missing_issue_keys'):
            report_lines.append("MISSING ISSUE KEYS:")
            for record_num in data_qual['missing_issue_keys']:
                report_lines.append(f"  Record {record_num}")
            report_lines.append("")
        
        if data_qual.get('invalid_states'):
            report_lines.append("INVALID STATES:")
            for item in data_qual['invalid_states']:
                report_lines.append(f"  Record {item['record']}: '{item['state']}'")
            report_lines.append("")
        
        # Derived fields
        derived = validation_results.get('derived_fields', {})
        if derived.get('formula_placeholders'):
            report_lines.append("FORMULA PLACEHOLDERS FOUND:")
            for item in derived['formula_placeholders']:
                report_lines.append(f"  Record {item['record']}, {item['field']}: {item['formula']}")
            report_lines.append("")


def validate_system_configuration():
    """Run comprehensive system validation"""
    validator = DataValidator()
    
    print("Running system configuration validation...")
    
    # Validate client configuration
    client_validation = validator.validate_client_configuration()
    client_report = validator.generate_validation_report(client_validation, "client_config")
    
    print(client_report)
    
    return client_validation['valid']


if __name__ == "__main__":
    validate_system_configuration()
