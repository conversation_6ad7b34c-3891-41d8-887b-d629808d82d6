import os
import requests
import urllib3
import json
import csv
import logging
import sys
from pathlib import Path

from datetime import datetime, date
from atlassian import <PERSON><PERSON>
from requests.auth import HTTPBasicAuth
from dotenv import load_dotenv
from jiraone import LOGIN, issue_export

from ..utils.gdrive_method import *
from ..config.client_config import ClientConfig, PDRMFirewallDataStructure
from ..utils.pdrm_firewall_formatter import PDRMFirewallFormatter

import pandas as pd

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
def setup_logging():
    """Setup comprehensive logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter('%(levelname)s: %(message)s')

    # Setup file handler
    file_handler = logging.FileHandler(log_dir / 'app.log')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)

    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    return root_logger

# Initialize logging
logger = setup_logging()

# Load environment variables
load_dotenv('app.env')

# Environment configuration with validation
class Config:
    """Configuration management with validation"""

    def __init__(self):
        self.jira_ad_username = os.environ.get("JIRA_USERNAME1")
        self.jira_username = os.environ.get("JIRA_USERNAME2")
        self.jira_password = os.environ.get("JIRA_PASSWORD")
        self.jira_server = os.environ.get("JIRA_SERVER")
        self.jira_token = os.environ.get("JIRA_TOKEN")
        self.jira_filter_token = os.environ.get("JIRA_FILTER_TOKEN")
        self.token_user = os.environ.get("TOKEN_USERNAME")

        # Validate required environment variables
        self.validate()

    def validate(self):
        """Validate that all required environment variables are set"""
        required_vars = {
            'JIRA_USERNAME1': self.jira_ad_username,
            'JIRA_SERVER': self.jira_server,
            'JIRA_TOKEN': self.jira_token
        }

        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")

        logger.info("Environment configuration validated successfully")

    @property
    def jira_headers(self):
        return {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

# Initialize configuration
try:
    config = Config()
except ValueError as e:
    logger.error(f"Configuration error: {e}")
    sys.exit(1)

def jira_api_auth():
    """Create authenticated Jira connection"""
    try:
        jira = Jira(config.jira_server, config.jira_ad_username, config.jira_token, cloud=True)
        logger.info("Jira authentication successful")
        return jira
    except Exception as e:
        logger.error(f"Jira authentication failed: {e}")
        raise

def list_jira_projects():
    """List all available Jira projects"""
    try:
        jira = jira_api_auth()
        projects = jira.projects()

        if not projects:
            logger.warning("No projects found")
            return

        logger.info(f"Found {len(projects)} projects")
        for project in projects:
            json_project = json.dumps(project)
            key = project['key']
            print(key)
    except Exception as e:
        logger.error(f"Failed to list projects: {e}")
        raise

def api_version():
    """Get Jira API version"""
    try:
        jira = jira_api_auth()
        logger.info(f"Jira API version: {jira.api_version}")
        print(jira.api_version)
    except Exception as e:
        logger.error(f"Failed to get Jira version: {e}")
        raise

def obtain_fav_filter():
    """Obtain favorite filters from Jira"""
    try:
        fav_filters_url = f"{config.jira_server}/rest/api/3/filter/favourite"
        filters_auth = HTTPBasicAuth(config.jira_username, config.jira_filter_token)

        filter_response = requests.request(
            "GET",
            fav_filters_url,
            headers=config.jira_headers,
            auth=filters_auth
        )

        filter_response.raise_for_status()

        fav_filter_list = []
        if filter_response.status_code == 200:
            favourites = filter_response.json()

            for fav in favourites:
                fav_filter_name = fav['name']
                jql_query = fav['jql']
                fav_search_url = fav['searchUrl']
                fav_view_url = fav['viewUrl']
                fav_filter_list.append((fav_filter_name,jql_query,fav_search_url,fav_view_url))

            if fav_filter_list:
                print(fav_filter_list[1])
                print(f"type : {type(fav_filter_list[1])}")
            logger.info(f"Successfully retrieved {len(fav_filter_list)} favorite filters")
        else:
            logger.warning("No favorite filters found")
    except requests.RequestException as e:
        logger.error(f"Failed to obtain favorite filters: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in obtain_fav_filter: {e}")
        raise

def return_server_info():
    """Get Jira server information"""
    try:
        server_info_url = f"{config.jira_server}/rest/api/3/serverInfo"
        # Implementation would go here
        logger.info("Server info function called")
    except Exception as e:
        logger.error(f"Failed to get server info: {e}")
        raise

def ensure_export_directory():
    """Ensure EXPORT directory exists"""
    export_dir = Path("EXPORT")
    export_dir.mkdir(exist_ok=True)
    logger.info(f"Export directory ensured: {export_dir.absolute()}")
    return export_dir

def validate_jira_config():
    """Validate Jira configuration file exists and is valid"""
    auth_file = "jira_one_config.json"
    if not os.path.exists(auth_file):
        raise FileNotFoundError(f"Jira config file not found: {auth_file}")

    try:
        with open(auth_file, 'r') as f:
            jira_one_config = json.load(f)

        # Basic validation - adjust keys based on actual config structure
        if not jira_one_config:
            raise ValueError("Jira config file is empty")

        logger.info("Jira configuration validated successfully")
        return jira_one_config
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in Jira config file: {e}")

def msoc_export_to_csv():
    """Export MSOC data to CSV with comprehensive error handling"""
    try:
        # Ensure export directory exists
        export_dir = ensure_export_directory()

        # Validate and load Jira configuration
        jira_one_config = validate_jira_config()
        LOGIN(**jira_one_config)
        logger.info("Jira authentication successful for MSOC export")

        # Use safe client list to avoid 400 errors from non-existent clients
        safe_clients = ClientConfig.get_safe_msoc_clients()
        client_list = ClientConfig.get_jql_client_list(safe_clients)
        jql_query = f'project = "MSOC" and "customer organization[dropdown]" in ({client_list}) and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY cf[10086] DESC, created DESC'

        # Debug: Print the JQL query to check for issues
        logger.debug(f"MSOC JQL Query: {jql_query}")
        logger.info(f"Using {len(safe_clients)} safe clients for MSOC export")

        # Show pending clients that are not included
        pending_clients = ClientConfig.get_pending_clients()
        if pending_clients:
            logger.info(f"{len(pending_clients)} clients pending addition to Jira: {', '.join(pending_clients)}")

        # Generate export file path
        current_datetime = datetime.now()
        export_filename = current_datetime.strftime("%d%m%y") + "_msoc_.csv"
        export_file_path = export_dir / export_filename

        try:
            # Attempt main export
            issue_export(jql=jql_query, final_file=str(export_file_path))

            # Validate export file was created and has content
            if not export_file_path.exists():
                raise FileNotFoundError(f"Export file was not created: {export_file_path}")

            if export_file_path.stat().st_size == 0:
                raise ValueError(f"Export file is empty: {export_file_path}")

            logger.info(f"MSOC export completed successfully: {export_file_path}")

        except Exception as e:
            logger.error(f"Error in MSOC export: {str(e)}")

            # Try with the most basic query as fallback
            fallback_query = 'project = "MSOC" and "customer organization[dropdown]" in (MAG, PDRM, Astro, Firefly, EPF) and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY created DESC'
            logger.info("Trying minimal fallback query...")

            try:
                issue_export(jql=fallback_query, final_file=str(export_file_path))
                logger.info("Fallback query succeeded")
            except Exception as fallback_error:
                logger.error(f"Fallback query also failed: {fallback_error}")
                raise

    except Exception as e:
        logger.error(f"Critical error in MSOC export process: {e}")
        raise

def pdrm_fw_export_to_csv():
    """Export PDRM Firewall data to CSV with error handling"""
    try:
        # Ensure export directory exists
        export_dir = ensure_export_directory()

        # Validate and load Jira configuration
        jira_one_config = validate_jira_config()
        LOGIN(**jira_one_config)
        logger.info("Jira authentication successful for PDRM export")

        jql_query = 'project = "MSOC" and "customer organization[dropdown]" = "PDRM Firewall" and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY created DESC'

        # Generate export file path
        current_datetime = datetime.now()
        export_filename = current_datetime.strftime("%d%m%y") + "_pdrm_.csv"
        export_file_path = export_dir / export_filename

        logger.debug(f"PDRM JQL Query: {jql_query}")

        issue_export(jql=jql_query, final_file=str(export_file_path))

        # Validate export file was created
        if not export_file_path.exists():
            raise FileNotFoundError(f"PDRM export file was not created: {export_file_path}")

        logger.info(f"PDRM export completed successfully: {export_file_path}")

    except Exception as e:
        logger.error(f"Critical error in PDRM export process: {e}")
        raise


def pdrm_fw_export_structured():
    """
    Export PDRM Firewall data in the new structured format with improved error handling
    """
    try:
        # First export using the standard method
        pdrm_fw_export_to_csv()

        # Get the current datetime for file naming
        current_datetime = datetime.now()
        curr_fmt_datetime = current_datetime.strftime("%d%m%y")

        # Use Path objects for better file handling
        export_dir = Path("EXPORT")
        raw_export_file = export_dir / f"{curr_fmt_datetime}_pdrm_.csv"
        structured_csv_file = export_dir / f"pdrm_firewall_structured_{curr_fmt_datetime}.csv"
        structured_json_file = export_dir / f"pdrm_firewall_structured_{curr_fmt_datetime}.json"

        # Check if raw export file exists
        if not raw_export_file.exists():
            error_msg = f"Raw export file not found: {raw_export_file}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        # Read the raw export data
        raw_data = []
        try:
            with open(raw_export_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                raw_data = list(reader)
        except Exception as e:
            logger.error(f"Failed to read raw export file: {e}")
            raise

        if not raw_data:
            logger.warning("No data found in raw export file")
            return None, None

        logger.info(f"Read {len(raw_data)} records from raw export file")

        # Initialize the formatter
        formatter = PDRMFirewallFormatter()

        # Transform the data to PDRM Firewall structure
        structured_data = formatter.transform_jira_data_to_pdrm_format(raw_data)

        # Validate the data
        validation_results = formatter.validate_data(structured_data)
        if not validation_results['valid']:
            logger.warning("Data validation failed:")
            for error in validation_results['errors']:
                logger.warning(f"  Record {error['record']}: Missing fields {error['fields']}")

        # Export to CSV and JSON (convert Path to string)
        formatter.export_to_csv(structured_data, str(structured_csv_file))
        formatter.export_to_json(structured_data, str(structured_json_file))

        logger.info(f"PDRM Firewall structured export completed:")
        logger.info(f"  CSV: {structured_csv_file}")
        logger.info(f"  JSON: {structured_json_file}")
        logger.info(f"  Records processed: {len(structured_data)}")

        return str(structured_csv_file), str(structured_json_file)

    except FileNotFoundError as e:
        logger.error(f"File not found during PDRM structured export: {e}")
        return None, None
    except Exception as e:
        logger.error(f"Error during PDRM Firewall structured export: {e}")
        return None, None


def find_csv_files(dir: str):
    csv_files = []
    if os.path.exists(dir):
        for file in os.listdir(dir):
            if file.endswith(".csv"):
                csv_files.append(file)

    else:
        print("The directory provide does not exist")

    if csv_files:
        return csv_files
    else:
        return False


def remove_single_column_if_exists(csv_file_path: str, column_name: str):
    temp_file_path = csv_file_path + '.tmp'
    found_column = False

    with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
        open(temp_file_path, 'w', newline='', encoding='utf-8') as temp_csvfile:
            reader = csv.reader(csvfile)
    writer = csv.writer(temp_csvfile)

    for i, row in enumerate(reader):
        if i == 0:  # Header row
            if column_name in row:
                found_column = True
                index_to_remove = row.index(column_name)
                new_row = [cell for j, cell in enumerate(row) if j != index_to_remove]
                writer.writerow(new_row)
            else:
                writer.writerow(row)
        else:
            new_row = [cell for j, cell in enumerate(row) if j != index_to_remove]
            writer.writerow(new_row)

    if found_column:
        os.remove(csv_file_path)
        os.rename(temp_file_path, csv_file_path)
        print(f"Column '{column_name}' removed from the CSV file.")
    else:
        os.remove(temp_file_path)
        print(f"Column '{column_name}' not found in the CSV file.")


def remove_columns_if_exist(csv_file_path: str, columns_to_remove: list):
    temp_file_path = csv_file_path + '.tmp'
    found_columns = {col.strip(): False for col in columns_to_remove}

    with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
         open(temp_file_path, 'w', newline='', encoding='utf-8') as temp_csvfile:
        reader = csv.reader(csvfile)
        writer = csv.writer(temp_csvfile)

        header = next(reader)
        header = [col.strip() for col in header]  # Strip whitespace from header

        for i, row in enumerate(reader):
            if i == 0:  # Header row
                for col in columns_to_remove:
                    if col.strip() in header:  # Compare stripped version
                        found_columns[col.strip()] = True

                new_header = [header[i] for i in range(len(header)) if header[i] not in columns_to_remove]
                writer.writerow(new_header)
            else:
                new_row = [row[i] for i in range(len(row)) if header[i] not in columns_to_remove]
                writer.writerow(new_row)

    columns_removed = [col for col, found in found_columns.items() if found]
    if any(found_columns.values()):
        os.remove(csv_file_path)
        os.rename(temp_file_path, csv_file_path)
        print(f"Columns '{', '.join(columns_removed)}' removed from the CSV file.")
    else:
        os.remove(temp_file_path)
        print(f"Columns '{', '.join(columns_to_remove)}' not found in the CSV file.")


def get_headers(csv_file_path: str) -> list:
    with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        headers = next(reader)
    return headers

def compare_lists(list1: list, list2: list):
    unique_in_list2 = [item for item in list2 if item not in list1]
    return unique_in_list2


def rearrange_columns(input_file: str, output_file: str, desired_column_order: list):
    df = pd.read_csv(input_file)

    missing_columns = [col for col in desired_column_order if col not in df.columns]
    if missing_columns:
        print("The following columns are missing from the input CSV file:", missing_columns)
        return

    df = df[desired_column_order]
    df.to_csv(output_file, index=False)


def transform_csv_to_excel_with_formulas(csv_file: str , excel_file: str):
    df = pd.read_csv(csv_file)

    if 'Custom field (SLA Status)' not in df.columns or 'Custom field (Duration)' not in df.columns:
        print("One or both of the specified columns not found in the DataFrame.")
        return

    # Get the indices of the specified columns
    sla_status_index = df.columns.get_loc('Custom field (SLA Status)')
    duration_index = df.columns.get_loc('Custom field (Duration)')
    notification_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)')
    alert_index = df.columns.get_loc('Custom field (Alert Generation Date/Time)')
    priority_index = df.columns.get_loc('Priority') # Column J

    # Transform the DataFrame into an Excel writer object
    with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')

        workbook = writer.book
        worksheet = writer.sheets['Sheet1']

        last_row = len(df) + 1

        # Formula for Custom field (SLA Status)
        for row in range(2, last_row):
            worksheet.write_formula(f'{chr(65 + sla_status_index)}{row}', f'=IF(LEN({chr(65 + priority_index)}{row}), IF(OR(AND(OR({chr(65 + priority_index)}{row}="High",{chr(65 + priority_index)}{row}="Highest"), {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(1,0,1)), AND({chr(65 + priority_index)}{row}="Medium", {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(2,0,1)), AND(OR({chr(65 + priority_index)}{row}="Low",{chr(65 + priority_index)}{row}="Lowest"), {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(23,0,1))), "Met", "Not Met"), "")')

        # Formula for Custom field (Duration)
        for row in range(2, last_row):
            # worksheet.write_formula(f'{chr(65 + duration_index)}{row}',f'=IF({chr(65 + alert_index)}{row}="", "", TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row},"hh:mm:ss"))')
            worksheet.write_formula(f'{chr(65 + duration_index)}{row}', f'=IF({chr(65 + alert_index)}{row}="", "", IF({chr(65 + notification_index)}{row}>{chr(65 + alert_index)}{row}, TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row},"hh:mm:ss"), TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}+1,"hh:mm:ss")))')

        # Autofit column widths
        for i, col in enumerate(df.columns):
            column_len = max(df[col].astype(str).map(len).max(), len(col))
            worksheet.set_column(i, i, column_len)

    print("Excel file with formulas generated successfully.")


def transform__pdrm_csv_to_excel_with_formulas(csv_file: str , excel_file: str):
    df = pd.read_csv(csv_file)

    if ('Custom field (TM/PDRM Holding Time)' not in df.columns) or ('Custom field (Total Time Taken)' not in df.columns) or ('Custom field (PDRM TL Holding Time)' not in df.columns):
        print("One of the specified columns not found in the DataFrame.")
        return

    # Get the indices of the specified columns
    holding_time_index = df.columns.get_loc('Custom field (TM/PDRM Holding Time)') # column N,
    ttl_time_index = df.columns.get_loc('Custom field (Total Time Taken)') # Column O, most important
    closing_time_index = df.columns.get_loc('Custom field (Closing Time)') # column J
    start_time_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)') # Column I
    pdrm_holding_index = df.columns.get_loc('Custom field (PDRM TL Holding Time)') # column Q
    tm_holding_time = df.columns.get_loc('Custom field (PDRM TL Holding Time)')
    tm_acknowledge_time = df.columns.get_loc('Custom field (PDRM Acknowledge Time)')

    # Transform the DataFrame into an Excel writer object
    with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')

        workbook = writer.book
        worksheet = writer.sheets['Sheet1']

        last_row = len(df) + 1

        # Formula for Custom field (Total Time Taken)
        for row in range(2, last_row):
            # worksheet.write_formula(f'{chr(65 + duration_index)}{row}',f'=IF({chr(65 + alert_index)}{row}="", "", TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row},"hh:mm:ss"))')
            worksheet.write_formula(f'{chr(65 + ttl_time_index)}{row}',f'=SUM({chr(65 + closing_time_index)}{row}-{chr(65 + start_time_index)}{row}),"[hh]:mm:ss")')

        # TM holding time
        for row in range(2,last_row):
            worksheet.write_formula(f'{chr(65 + pdrm_holding_index)}{row}',f'=IF(OR({chr(65 + tm_acknowledge_time)}{row}="",C1221=""),"",TEXT({chr(65 + ttl_time_index)}{row}-{chr(65 + start_time_index)}{row},"hh:mm:ss"))')

        # Formula for TM/PDRM
        # for row in range(2, last_row):
        #     worksheet.write_formula(f'{chr(65 + holding_time_index)}{row}', f'=IF(OR(ISBLANK({chr(65 + ttl_time_index)}{row}), ISBLANK({chr(65 + tm_holding_time)}{row})), 0, TEXT(({chr(65 + ttl_time_index)}{row}-{chr(65 + tm_holding_time)}{row}), "[hh]:mm:ss"))')
        for row in range(2, last_row):
            worksheet.write_formula(f'{chr(65 + holding_time_index)}{row}', f'=IF({chr(65 + ttl_time_index)}{row}=0, 0, IF(OR(ISBLANK({chr(65 + tm_holding_time)}{row}), {chr(65 + tm_holding_time)}{row}=0), 0, TEXT(({chr(65 + ttl_time_index)}{row}-{chr(65 + tm_holding_time)}{row}), "[hh]:mm:ss")))')


        # Autofit column widths
        for i, col in enumerate(df.columns):
            column_len = max(df[col].astype(str).map(len).max(), len(col))
            worksheet.set_column(i, i, column_len)

    print("Excel file with formulas generated successfully.")


def convert_date_format(input_file, output_file, date_column ='Custom field (Notification Sent out Date/Time)', month_column = 'Custom field (Month)'):
    # Define the date parsing and formatting functions
    def parse_date(date_str):
        return datetime.strptime(date_str, "%d/%b/%y %I:%M %p")
        # return datetime.strptime(date_str, "%d/%m/%Y %H:%M")

    def format_date(date_obj):
        return str(date_obj.strftime("%b %Y"))

    # Read the CSV file, convert dates, and write to the output CSV file
    encodings_to_try = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

    for encoding in encodings_to_try:
        try:
            print(f"Trying to read CSV with {encoding} encoding...")
            with open(input_file, 'r', encoding=encoding) as infile, open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                reader = csv.DictReader(infile)
                fieldnames = reader.fieldnames

                writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                writer.writeheader()

                for row in reader:
                    if row[month_column]:  # Check if the "Custom field (Month)" column has a value
                        date_str = row[date_column]
                        date_obj = parse_date(date_str)
                        formatted_date = format_date(date_obj)
                        row[month_column] = formatted_date

                    writer.writerow(row)

            print(f"Successfully processed file with {encoding} encoding")
            return  # Exit the function if successful

        except UnicodeDecodeError as e:
            print(f"Failed with {encoding} encoding: {str(e)}")
            continue  # Try the next encoding

    # If we get here, all encodings failed
    print("ERROR: Failed to process the CSV file with all attempted encodings.")
    print("You may need to manually convert the file encoding or check for corrupt characters.")


def delete_current_day_csv_files(directory):
    # Get the current date
    current_date = date.today()

    # Iterate over all files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.csv'):
            filepath = os.path.join(directory, filename)
            # Get the modification time of the file
            modification_time = os.path.getmtime(filepath)
            modification_date = datetime.fromtimestamp(modification_time).date()
            # Check if the modification date is the same as the current date
            if modification_date == current_date:
                # Delete the file
                os.remove(filepath)
                print(f"Deleted: {filepath}")


def upload_to_drive(file_path:str):
    # Upload the Excel file to Google Drive
    file_to_be_uploaded_paths = [file_path]
    destination_folder_id = '1-OFA499xDXc6ktLv19ShSQ8B58sk1ibR'
    uploaded_files = upload_files_to_drive(file_to_be_uploaded_paths, destination_folder_id)

    # Change to proper logging for monitoring purpose
    for file_info in uploaded_files:
        print('Uploaded:', file_info['title'])
        print('File ID:', file_info['id'])
        print('Link:', file_info['webContentLink'])
        print()


def modify_resolution_column(input_file, output_file,column_name):
    with open(input_file, 'r', newline='', encoding='utf-8') as infile, \
         open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        reader = csv.DictReader(infile)
        fieldnames = reader.fieldnames
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()

        for row in reader:
            resolution = row[column_name]
            # Split the content of the resolution column based on ";"
            parts = resolution.rsplit(';', 1)
            if len(parts) > 1:
                # Get the last element after splitting
                modified_resolution = parts[-1].strip()
                row[column_name] = modified_resolution
            # Write the modified row to the new CSV file
            writer.writerow(row)


################################################# main function here ###########################


def main_msoc_process():
    """Main MSOC processing function with comprehensive error handling"""
    try:
        logger.info("Starting MSOC processing...")

        # Generate the MSOC CSV
        msoc_export_to_csv()

        # Use Path objects for better file handling
        export_dir = Path("EXPORT")

        desired_column_order = ['Issue key','Custom field (Client Ticket Number)', 'Custom field (Tool Incident ID)', 'Custom field (Customer Organization)', 'Summary', 'Custom field (Alert Generation Date/Time)', 'Custom field (Notification Sent out Date/Time)', 'Custom field (Duration)', 'Custom field (SLA Status)', 'Priority', 'Custom field (Analyst Name)', 'Custom field (Incident Source/Tool)', 'Custom field (Source IP/Hostname)', 'Custom field (Username/Hostname/Log Source)', 'Custom field (Destination IP)', 'Custom field (EPF Collector IP)', 'Custom field (PDRM Fault Category)', 'Custom field (PDRM Acknowledge Venue)', 'Custom field (Detection/Impact)', 'Status', 'Custom field (Month)', 'Custom field (Resolutions Notes)', 'Custom field (Last Follow Up)', 'Custom field (Closing Time)', 'Resolution','Comment']

        current_datetime = datetime.now()
        curr_fmt_datetime = current_datetime.strftime("%d%m%y")
        curr_msoc_file = curr_fmt_datetime + "_msoc_"

        input_msoc_file = export_dir / f"{curr_msoc_file}.csv"
        output_msoc_file = export_dir / "modified_msoc.csv"

        # Check if the input file exists and has content
        if not input_msoc_file.exists():
            error_msg = f"MSOC export file not found: {input_msoc_file}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        # Check if file has content
        try:
            with open(input_msoc_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    error_msg = f"MSOC export file is empty: {input_msoc_file}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # Check if it has proper CSV structure
                lines = content.split('\n')
                if len(lines) < 2:  # At least header + 1 data row
                    logger.warning(f"MSOC export file has only {len(lines)} lines")
        except Exception as e:
            logger.error(f"Failed to read MSOC export file: {e}")
            raise

        logger.info(f"Processing MSOC file: {input_msoc_file}")

        # Process the file through various transformations
        modify_resolution_column(str(input_msoc_file), str(output_msoc_file), column_name='Custom field (Resolutions Notes)')

        rearrange_columns(str(output_msoc_file), str(output_msoc_file), desired_column_order)

        date_converted_msoc = export_dir / 'date_conv_msoc.csv'
        convert_date_format(input_file=str(output_msoc_file), output_file=str(date_converted_msoc))

        modified_msoc_excel = export_dir / f"modified_msoc_{curr_fmt_datetime}.xlsx"
        transform_csv_to_excel_with_formulas(str(date_converted_msoc), str(modified_msoc_excel))

        # Upload to Google Drive
        upload_to_drive(str(modified_msoc_excel))

        logger.info("MSOC processing completed successfully")

    except Exception as e:
        logger.error(f"Critical error in MSOC processing: {e}")
        raise



def main_pdrm_process():
    # generate the pdrm fw csv
    pdrm_fw_export_to_csv()

    excel_dir= f"{os.getcwd()}/EXPORT" # directory to store the excel sheet

    desired_column_order = ['Issue key', 'Custom field (Client Ticket Number)', 'Custom field (Notification Sent out Date/Time)', 'Custom field (Last Follow Up)', 'Priority', 'Custom field (Analyst Name)', 'Summary', 'Custom field (PDRM Fault Category)', 'Custom field (Closing Time)', 'Custom field (PDRM Acknowledge Venue)', 'Status', 'Custom field (Resolutions Notes)', 'Custom field (TM/PDRM Holding Time)', 'Custom field (Total Time Taken)', 'Custom field (PDRM Acknowledge Time)', 'Custom field (PDRM TL Holding Time)', 'Custom field (Customer Organization)', 'Custom field (Month)','Comment']

    current_datetime = datetime.now()
    curr_fmt_datetime =  current_datetime.strftime("%d%m%y")
    curr_pdrm_file = curr_fmt_datetime + "_pdrm_"

    input_pdrm_file = f"{excel_dir}/{curr_pdrm_file}.csv" # how to obtain the msoc csv file?
    output_pdrm_file = f"{excel_dir}/modified_pdrm.csv"

    modify_resolution_column(input_pdrm_file, output_pdrm_file,column_name='Comment')

    rearrange_columns(output_pdrm_file, output_pdrm_file, desired_column_order)

    date_converted_pdrm = f'{excel_dir}/date_conv_pdrm.csv'  # Replace with the desired output CSV file
    convert_date_format(input_file=output_pdrm_file, output_file=date_converted_pdrm)

    modified_pdrm_excel = f"{excel_dir}/modified_pdrm_{curr_fmt_datetime}.xlsx"
    transform__pdrm_csv_to_excel_with_formulas(date_converted_pdrm,modified_pdrm_excel)

    # gdrive upload directory
    upload_to_drive(modified_pdrm_excel)


def main_pdrm_structured_process():
    """
    Enhanced PDRM process using the new structured data format
    """
    print("Starting PDRM Firewall structured export process...")

    # Generate structured PDRM Firewall export
    structured_csv, structured_json = pdrm_fw_export_structured()

    if structured_csv and structured_json:
        print(f"Structured export completed successfully:")
        print(f"  CSV: {structured_csv}")
        print(f"  JSON: {structured_json}")

        # Optional: Upload structured files to Google Drive
        try:
            upload_to_drive(structured_csv)
            upload_to_drive(structured_json)
            print("Files uploaded to Google Drive successfully")
        except Exception as e:
            print(f"Warning: Failed to upload to Google Drive: {str(e)}")
    else:
        print("Structured export failed")


def validate_and_show_client_config():
    """
    Validate and display current client configuration
    """
    from data_validation import DataValidator

    validator = DataValidator()

    # Validate client configuration
    validation_results = validator.validate_client_configuration()

    # Generate and display report
    report = validator.generate_validation_report(validation_results, "client_config")
    print(report)

    # Show current client lists
    print("\n=== CURRENT CLIENT CONFIGURATION ===")
    print(f"All Clients ({len(ClientConfig.get_all_clients())}):")
    for client in ClientConfig.get_all_clients():
        client_type = ClientConfig.ALL_CLIENTS[client].value
        print(f"  - {client} ({client_type})")

    print(f"\nMSOC JQL Query Client List:")
    print(f"  {ClientConfig.get_jql_client_list()}")

    return validation_results['valid']

if __name__ == "__main__":
    logger.info("=== SOC Jira Tracker - Enhanced Version ===")
    logger.info("NOTE: make sure the current file in the gdrive is moved to the archive folder")

    try:
        # Ensure export directory exists first
        ensure_export_directory()

        # Validate system configuration first
        logger.info("1. Validating system configuration...")
        config_valid = validate_and_show_client_config()

        if not config_valid:
            logger.error("System configuration validation failed. Please check the configuration.")
            sys.exit(1)

        # Configuration flags
        pdrm_trigger = True
        msoc_trigger = True
        use_structured_pdrm = True  # New flag for structured PDRM export

        logger.info("2. Export Configuration:")
        logger.info(f"   - MSOC Export: {'Enabled' if msoc_trigger else 'Disabled'}")
        logger.info(f"   - PDRM Export: {'Enabled' if pdrm_trigger else 'Disabled'}")
        logger.info(f"   - PDRM Structured Export: {'Enabled' if use_structured_pdrm else 'Disabled'}")

        # Run exports
        if msoc_trigger:
            logger.info("3. Running MSOC export process...")
            try:
                main_msoc_process()
                logger.info("   MSOC export completed successfully")
            except Exception as e:
                logger.error(f"   ERROR in MSOC export: {str(e)}")

        if pdrm_trigger:
            logger.info("4. Running PDRM export process...")
            try:
                if use_structured_pdrm:
                    main_pdrm_structured_process()
                    logger.info("   PDRM structured export completed successfully")
                else:
                    main_pdrm_process()
                    logger.info("   PDRM traditional export completed successfully")
            except Exception as e:
                logger.error(f"   ERROR in PDRM export: {str(e)}")

        # Cleanup
        logger.info("5. Cleaning up temporary files...")
        export_dir = Path("EXPORT")
        delete_current_day_csv_files(str(export_dir))
        logger.info("   Cleanup completed")

        logger.info("=== Export Process Completed ===")
        logger.info("Check the EXPORT directory for generated files.")
        logger.info("Structured PDRM files are available in both CSV and JSON formats.")

    except Exception as e:
        logger.error(f"Critical error in main process: {e}")
        sys.exit(1)
    print("Check the EXPORT directory for generated files.")
    print("Structured PDRM files are available in both CSV and JSON formats.")









