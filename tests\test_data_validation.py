"""
Test module for data validation functionality
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.data_validation import DataValidator


class TestDataValidation(unittest.TestCase):
    """Test cases for DataValidator class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.validator = DataValidator()
    
    def test_validate_client_configuration(self):
        """Test client configuration validation"""
        result = self.validator.validate_client_configuration()
        self.assertIsInstance(result, dict)
        self.assertIn('valid', result)
        self.assertIn('total_clients', result)
        self.assertIn('msoc_clients', result)
        self.assertIn('firewall_clients', result)
    
    def test_validate_csv_structure(self):
        """Test CSV structure validation"""
        # Test with valid headers
        valid_headers = ['Issue key', 'Summary', 'Status']
        required_fields = ['Issue key', 'Summary']
        
        result = self.validator.validate_csv_structure(valid_headers, required_fields)
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['missing_fields']), 0)
        
        # Test with missing headers
        invalid_headers = ['Issue key']
        result = self.validator.validate_csv_structure(invalid_headers, required_fields)
        self.assertFalse(result['valid'])
        self.assertIn('Summary', result['missing_fields'])
    
    def test_validate_date_format(self):
        """Test date format validation"""
        # Test valid date
        valid_date = "2024-01-15 10:30:00"
        self.assertTrue(self.validator.validate_date_format(valid_date))
        
        # Test invalid date
        invalid_date = "invalid-date"
        self.assertFalse(self.validator.validate_date_format(invalid_date))
        
        # Test empty date
        self.assertFalse(self.validator.validate_date_format(""))
        self.assertFalse(self.validator.validate_date_format(None))


if __name__ == '__main__':
    unittest.main()
